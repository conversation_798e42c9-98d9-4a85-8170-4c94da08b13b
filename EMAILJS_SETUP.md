# EmailJS Setup Guide for Contact Form

This guide will help you set up EmailJS to send emails from your contact form directly to helpwith<PERSON><PERSON><EMAIL>.

## Step 1: Create EmailJS Account

1. Go to [https://www.emailjs.com/](https://www.emailjs.com/)
2. Sign up for a free account
3. Verify your email address

## Step 2: Add Email Service

1. In your EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose your email provider:
   - For Proton Mail, select "Other" or "Custom SMTP"
   - Or you can use Gmail/Outlook as a relay service

### For Proton Mail (Recommended):
- Service ID: `service_mufasa_portfolio`
- SMTP Server: `mail.protonmail.ch`
- Port: `587`
- Username: `<EMAIL>`
- Password: Your Proton Mail app password (create one in Proton settings)

### Alternative - Gmail Relay (Easier Setup):
- Choose Gmail service
- Connect your Gmail account
- Emails will be forwarded to your Proton Mail

## Step 3: Create Email Template

1. Go to "Email Templates"
2. Click "Create New Template"
3. Template ID: `template_contact_form`
4. Use this template content:

```
Subject: New Project Inquiry from {{from_name}}

Hello Mufasa,

You have received a new project inquiry through your portfolio website.

**Contact Information:**
- Name: {{from_name}}
- Email: {{from_email}}
- Company: {{company}}
- Phone: {{phone}}

**Project Details:**
- Project Type: {{project_type}}
- Budget Range: {{budget}}
- Timeline: {{timeline}}
- Priority Level: {{urgency}}
- Services Needed: {{services}}

**Subject:** {{subject}}

**Message:**
{{message}}

---
This message was sent from your portfolio contact form.
Reply directly to this email to respond to {{from_name}}.
```

## Step 4: Get Your Keys

1. Go to "Account" → "General"
2. Copy your Public Key
3. Go to "Email Services" and copy your Service ID
4. Go to "Email Templates" and copy your Template ID

## Step 5: Update Environment Variables

Update the `.env.local` file with your actual values:

```env
VITE_EMAILJS_SERVICE_ID=your_actual_service_id
VITE_EMAILJS_TEMPLATE_ID=template_contact_form
VITE_EMAILJS_PUBLIC_KEY=your_actual_public_key
```

## Step 6: Test the Form

1. Start your development server: `npm run dev`
2. Fill out the contact form
3. Check your Proton Mail inbox for the test email

## Troubleshooting

### Common Issues:

1. **Emails not sending:**
   - Check your EmailJS dashboard for error logs
   - Verify your service configuration
   - Make sure your public key is correct

2. **Emails going to spam:**
   - Add your domain to EmailJS allowed origins
   - Set up SPF/DKIM records (advanced)

3. **Rate limiting:**
   - Free EmailJS accounts have monthly limits
   - Consider upgrading for higher volume

### Security Notes:

- The public key is safe to expose in frontend code
- Never expose your private key
- Consider adding reCAPTCHA for spam protection

## Monthly Limits (Free Plan):

- 200 emails per month
- 50 emails per day
- Upgrade to paid plan for higher limits

## Support:

If you encounter issues:
1. Check EmailJS documentation
2. Test with a simple HTML form first
3. Contact EmailJS support if needed

---

Once configured, your contact form will send emails <NAME_EMAIL> with all the form details formatted nicely.

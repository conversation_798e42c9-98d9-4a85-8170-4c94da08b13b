import { Button } from "@/components/ui/button";
import { ArrowRight, Download } from "lucide-react";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";

const Hero = () => {
  const heroRef = useScrollAnimation();

  return (
    <section 
      id="home" 
      className="min-h-screen flex items-center justify-center pt-16 relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-20 left-10 w-72 h-72 gradient-primary rounded-full opacity-10 blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full opacity-20 blur-3xl animate-float delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full gradient-radial from-primary/5 to-transparent"></div>
      </div>

      <div 
        ref={heroRef}
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 opacity-0 translate-y-8"
      >
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-8 animate-fade-in-left">
            <div className="space-y-6">
              <div className="space-y-2">
                <div className="inline-block px-4 py-2 rounded-full bg-secondary/50 text-secondary-foreground text-sm font-medium animate-bounce-in delay-300">
                  👋 Welcome to my digital space
                </div>
                <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold leading-tight">
                  <span className="block text-foreground animate-fade-in-up delay-500">Hello.</span>
                  <span className="block text-foreground animate-fade-in-up delay-700">I'm Mufasa</span>
                  <span className="block gradient-primary bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent animate-fade-in-up delay-1000 animate-gradient-x">
                    Software Developer
                  </span>
                </h1>
              </div>
              <p className="text-xl text-muted-foreground max-w-lg leading-relaxed animate-fade-in-up delay-1200">
                Full-Stack Developer, Business Analyst, and Digital Consultant 
                dedicated to transforming ideas into digital reality through 
                cutting-edge technology and data-driven strategies.
              </p>
            </div>

            {/* CTAs */}
            <div className="flex flex-col sm:flex-row gap-4 animate-fade-in-up delay-1400">
              <Button 
                size="lg"
                className="gradient-primary text-primary-foreground border-0 hover:shadow-glow transition-smooth group animate-glow-pulse relative overflow-hidden"
              >
                <span className="relative z-10 flex items-center">
                  Get a project
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-primary to-accent opacity-0 group-hover:opacity-20 transition-opacity"></div>
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                className="border-border hover:bg-secondary hover:scale-105 transition-all duration-300 hover:shadow-elegant group"
              >
                <Download className="mr-2 h-4 w-4 group-hover:animate-bounce" />
                My resume
              </Button>
            </div>

            {/* Tech Stack */}
            <div className="flex flex-wrap gap-3 pt-8 animate-fade-in-up delay-1600">
              {["HTML5", "CSS3", "JavaScript", "Node.js", "React", "Git", "GitHub"].map((tech, index) => (
                <span
                  key={tech}
                  className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-full hover:bg-primary hover:text-primary-foreground transition-all duration-300 cursor-default hover:scale-110"
                  style={{
                    animationDelay: `${1800 + index * 100}ms`
                  }}
                >
                  {tech}
                </span>
              ))}
            </div>
          </div>

          {/* Profile Image */}
          <div className="flex justify-center lg:justify-end animate-fade-in-right">
            <div className="relative group">
              <div className="w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden border-4 border-primary/20 shadow-glow transition-all duration-500 group-hover:border-primary/40 group-hover:shadow-glow-accent group-hover:scale-105">
                <img
                  src="/mufasadev-pic.jpeg"
                  alt="Mufasa - Software Developer"
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
              </div>
              {/* Enhanced Decorative elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 gradient-primary rounded-full opacity-20 blur-xl animate-float"></div>
              <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-accent/20 rounded-full opacity-30 blur-2xl animate-float delay-1000"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full rounded-full bg-gradient-to-r from-primary/10 to-accent/10 animate-glow-pulse opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
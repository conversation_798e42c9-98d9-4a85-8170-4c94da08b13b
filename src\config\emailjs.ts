// EmailJS Configuration
export const emailjsConfig = {
  serviceId: import.meta.env.VITE_EMAILJS_SERVICE_ID || 'service_mufasa_portfolio',
  templateId: import.meta.env.VITE_EMAILJS_TEMPLATE_ID || 'template_contact_form',
  publicKey: import.meta.env.VITE_EMAILJS_PUBLIC_KEY || 'your_public_key_here'
};

// Fallback configuration for development
export const fallbackConfig = {
  serviceId: 'service_mufasa_portfolio',
  templateId: 'template_contact_form', 
  publicKey: 'your_public_key_here'
};

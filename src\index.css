@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 220 26% 7%;
    --foreground: 210 40% 98%;

    --card: 224 27% 9%;
    --card-foreground: 210 40% 98%;

    --popover: 224 27% 9%;
    --popover-foreground: 210 40% 98%;

    --primary: 263 70% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 218 27% 12%;
    --secondary-foreground: 210 40% 98%;

    --muted: 218 27% 12%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 12 76% 61%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 218 27% 12%;
    --input: 218 27% 12%;
    --ring: 263 70% 50%;

    --radius: 0.75rem;

    /* Custom Design Tokens */
    --gradient-primary: linear-gradient(135deg, hsl(263 70% 50%), hsl(12 76% 61%));
    --gradient-subtle: linear-gradient(180deg, hsl(220 26% 7%), hsl(224 27% 9%));
    --gradient-card: linear-gradient(145deg, hsl(224 27% 9%), hsl(218 27% 12%));
    
    --glow-primary: 0 0 40px hsl(263 70% 50% / 0.3);
    --glow-accent: 0 0 40px hsl(12 76% 61% / 0.3);
    
    --shadow-elegant: 0 20px 40px -12px hsl(220 26% 7% / 0.8);
    --shadow-glow: 0 0 60px hsl(263 70% 50% / 0.2);
    
    --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* Light mode fallback - keeping same dark theme */
  .light {
    --background: 220 26% 7%;
    --foreground: 210 40% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
    background: var(--gradient-subtle);
    min-height: 100vh;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  .gradient-primary {
    background: var(--gradient-primary);
  }
  
  .gradient-card {
    background: var(--gradient-card);
  }
  
  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }
  
  .transition-smooth {
    transition: var(--transition-smooth);
  }
  
  .transition-fast {
    transition: var(--transition-fast);
  }
}
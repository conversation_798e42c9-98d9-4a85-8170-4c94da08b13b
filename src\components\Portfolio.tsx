import { ExternalLink, Github } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useScrollAnimation, useStaggeredAnimation } from "@/hooks/useScrollAnimation";

const Portfolio = () => {
  const headerRef = useScrollAnimation();
  const gridRef = useStaggeredAnimation(400);
  const buttonRef = useStaggeredAnimation(800);

  const projects = [
    {
      title: "E-Commerce Platform",
      description: "Full-stack e-commerce solution with React, Node.js, and MongoDB. Features include user authentication, payment processing, and admin dashboard.",
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop&crop=center",
      technologies: ["React", "Node.js", "MongoDB", "Stripe"],
      liveUrl: "#",
      githubUrl: "#",
      category: "Full Stack"
    },
    {
      title: "Task Management App",
      description: "Collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.",
      image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop&crop=center",
      technologies: ["React", "TypeScript", "Socket.io", "PostgreSQL"],
      liveUrl: "#",
      githubUrl: "#",
      category: "Web App"
    },
    {
      title: "Analytics Dashboard",
      description: "Business analytics dashboard with interactive charts, data visualization, and real-time reporting capabilities for enterprise clients.",
      image: "https://images.unsplash.com/photo-**********-bebda4e38f71?w=600&h=400&fit=crop&crop=center",
      technologies: ["Next.js", "D3.js", "Python", "PostgreSQL"],
      liveUrl: "#",
      githubUrl: "#",
      category: "Dashboard"
    },
    {
      title: "Mobile Banking App",
      description: "Secure mobile banking application with biometric authentication, transaction history, and financial planning tools.",
      image: "https://images.unsplash.com/photo-**********-824ae1b704d3?w=600&h=400&fit=crop&crop=center",
      technologies: ["React Native", "Node.js", "JWT", "MongoDB"],
      liveUrl: "#",
      githubUrl: "#",
      category: "Mobile"
    },
    {
      title: "Restaurant Ordering System",
      description: "Complete restaurant management system with online ordering, inventory management, and customer relationship features.",
      image: "https://images.unsplash.com/photo-*************-338989a2e8c0?w=600&h=400&fit=crop&crop=center",
      technologies: ["Vue.js", "Laravel", "MySQL", "Payment Integration"],
      liveUrl: "#",
      githubUrl: "#",
      category: "Full Stack"
    },
    {
      title: "Learning Management System",
      description: "Educational platform with course management, video streaming, progress tracking, and interactive learning tools.",
      image: "https://images.unsplash.com/photo-1501504905252-473c47e087f8?w=600&h=400&fit=crop&crop=center",
      technologies: ["React", "Express", "MongoDB", "AWS S3"],
      liveUrl: "#",
      githubUrl: "#",
      category: "Platform"
    }
  ];

  return (
    <section id="portfolio" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/20 to-transparent"></div>
        <div className="absolute top-1/3 left-0 w-72 h-72 bg-primary/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/3 right-0 w-80 h-80 bg-accent/5 rounded-full blur-3xl animate-float delay-1000"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div 
          ref={headerRef}
          className="text-center space-y-6 mb-16 opacity-0 translate-y-8"
        >
          <div className="inline-block">
            <h2 className="text-4xl lg:text-5xl font-bold text-foreground relative">
              Portfolio
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-1 gradient-primary rounded-full animate-scale-in delay-300"></div>
            </h2>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto hover:text-foreground transition-colors duration-300 cursor-default">
            A showcase of my recent projects and creative solutions
          </p>
        </div>

        {/* Projects Grid */}
        <div 
          ref={gridRef}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 opacity-0 translate-y-8"
        >
          {projects.map((project, index) => (
            <Card 
              key={index}
              className="group overflow-hidden gradient-card border-border/50 hover:border-primary/30 transition-all duration-500 hover:scale-105 hover:shadow-glow relative"
              style={{
                animationDelay: `${600 + index * 150}ms`
              }}
            >
              {/* Category Badge */}
              <div className="absolute top-4 left-4 z-20 px-2 py-1 text-xs font-medium bg-primary/20 text-primary-foreground rounded-full backdrop-blur-sm">
                {project.category}
              </div>

              {/* Image with overlay */}
              <div className="aspect-video overflow-hidden relative">
                <img 
                  src={project.image} 
                  alt={project.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                
                {/* Hover Actions */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 transform scale-75 group-hover:scale-100">
                  <div className="flex gap-3">
                    <Button 
                      size="sm" 
                      className="bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border-white/20"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                    <Button 
                      size="sm"
                      className="bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border-white/20"
                    >
                      <Github className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <CardContent className="p-6 space-y-4">
                <div className="space-y-2">
                  <h3 className="text-xl font-semibold text-foreground group-hover:gradient-primary group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-accent group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300">
                    {project.title}
                  </h3>
                  <p className="text-muted-foreground text-sm leading-relaxed group-hover:text-foreground transition-colors duration-300">
                    {project.description}
                  </p>
                </div>

                {/* Technologies */}
                <div className="flex flex-wrap gap-2">
                  {project.technologies.map((tech, techIndex) => (
                    <span 
                      key={techIndex}
                      className="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded-md hover:bg-primary hover:text-primary-foreground transition-colors duration-300 cursor-default"
                      style={{
                        transitionDelay: `${techIndex * 100}ms`
                      }}
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                {/* Desktop Actions */}
                <div className="flex gap-3 pt-2 lg:opacity-0 lg:group-hover:opacity-100 lg:transition-opacity lg:duration-300">
                  <Button 
                    size="sm" 
                    variant="outline"
                    className="flex-1 hover:bg-primary hover:text-primary-foreground hover:scale-105 transition-all duration-300"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Live Demo
                  </Button>
                  <Button 
                    size="sm" 
                    variant="ghost"
                    className="hover:bg-secondary hover:scale-105 transition-all duration-300"
                  >
                    <Github className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View More Button */}
        <div 
          ref={buttonRef}
          className="text-center mt-12 opacity-0 translate-y-8"
        >
          <Button 
            variant="outline" 
            size="lg"
            className="border-primary/30 text-primary hover:bg-primary hover:text-primary-foreground hover:scale-105 hover:shadow-glow transition-all duration-500 group"
          >
            <span className="mr-2">View More Projects</span>
            <ExternalLink className="w-4 h-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
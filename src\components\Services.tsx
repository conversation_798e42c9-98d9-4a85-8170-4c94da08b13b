import { Code, BarChart3, FileText, Smartphone, Database, Zap } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { useScrollAnimation, useStaggeredAnimation } from "@/hooks/useScrollAnimation";

const Services = () => {
  const headerRef = useScrollAnimation();
  const gridRef = useStaggeredAnimation(300);

  const services = [
    {
      icon: <Code className="w-10 h-10" />,
      title: "Web Development",
      description: "Custom web applications and websites built with modern technologies and best practices for optimal performance and user experience.",
      features: ["React & Next.js", "Node.js & Express", "Database Design", "API Development"],
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: <BarChart3 className="w-10 h-10" />,
      title: "Business Analysis",
      description: "Strategic analysis and solutions to optimize your business processes and achieve measurable results through data-driven insights.",
      features: ["Process Optimization", "Data Analytics", "Strategic Planning", "Performance Metrics"],
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: <FileText className="w-10 h-10" />,
      title: "Resume Writing",
      description: "Professional resume writing and optimization to help you stand out in the competitive job market with ATS-friendly formats.",
      features: ["ATS Optimization", "Industry-Specific", "Cover Letters", "LinkedIn Profiles"],
      color: "from-green-500 to-teal-500"
    },
    {
      icon: <Smartphone className="w-10 h-10" />,
      title: "Mobile Development",
      description: "Cross-platform mobile applications that deliver native performance and exceptional user experiences across all devices.",
      features: ["React Native", "iOS & Android", "Progressive Web Apps", "Mobile-First Design"],
      color: "from-orange-500 to-red-500"
    },
    {
      icon: <Database className="w-10 h-10" />,
      title: "Database Solutions",
      description: "Robust database architecture and optimization solutions to ensure your data is secure, scalable, and performant.",
      features: ["Database Design", "Performance Tuning", "Data Migration", "Backup Strategies"],
      color: "from-indigo-500 to-purple-500"
    },
    {
      icon: <Zap className="w-10 h-10" />,
      title: "Digital Transformation",
      description: "Comprehensive digital transformation guidance to modernize your business operations and stay competitive in the digital age.",
      features: ["Technology Audit", "Process Automation", "Cloud Migration", "Digital Strategy"],
      color: "from-yellow-500 to-orange-500"
    }
  ];

  return (
    <section id="services" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/20 to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-full h-px bg-gradient-to-r from-transparent via-accent/20 to-transparent"></div>
        <div className="absolute top-1/4 left-10 w-96 h-96 bg-primary/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 right-10 w-80 h-80 bg-accent/5 rounded-full blur-3xl animate-float delay-1000"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div 
          ref={headerRef}
          className="text-center space-y-6 mb-16 opacity-0 translate-y-8"
        >
          <div className="inline-block">
            <h2 className="text-4xl lg:text-5xl font-bold text-foreground relative">
              Services
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-1 gradient-primary rounded-full animate-scale-in delay-300"></div>
            </h2>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto hover:text-foreground transition-colors duration-300 cursor-default">
            Comprehensive solutions for your digital needs
          </p>
        </div>

        {/* Services Grid */}
        <div 
          ref={gridRef}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 opacity-0 translate-y-8"
        >
          {services.map((service, index) => (
            <Card 
              key={index}
              className="group gradient-card border-border/50 hover:border-primary/30 transition-all duration-500 hover:scale-105 hover:shadow-glow cursor-pointer overflow-hidden relative"
              style={{
                animationDelay: `${600 + index * 150}ms`
              }}
            >
              {/* Hover overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              
              <CardHeader className="space-y-4 relative z-10">
                <div className="w-16 h-16 rounded-2xl gradient-primary flex items-center justify-center text-primary-foreground group-hover:scale-110 group-hover:rotate-6 transition-all duration-500">
                  {service.icon}
                </div>
                <CardTitle className="text-xl font-semibold text-foreground group-hover:gradient-primary group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-accent group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300">
                  {service.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 relative z-10">
                <p className="text-muted-foreground leading-relaxed group-hover:text-foreground transition-colors duration-300">
                  {service.description}
                </p>
                <ul className="space-y-3">
                  {service.features.map((feature, featureIndex) => (
                    <li 
                      key={featureIndex} 
                      className="flex items-center text-sm text-muted-foreground group-hover:text-foreground transition-all duration-300"
                      style={{
                        transitionDelay: `${featureIndex * 100}ms`
                      }}
                    >
                      <div className="w-1.5 h-1.5 rounded-full bg-primary mr-3 group-hover:bg-accent group-hover:scale-150 transition-all duration-300"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
              
              {/* Animated corner accent */}
              <div className="absolute top-0 right-0 w-0 h-0 border-t-[40px] border-r-[40px] border-t-primary/10 border-r-transparent group-hover:border-t-primary/30 transition-all duration-500"></div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
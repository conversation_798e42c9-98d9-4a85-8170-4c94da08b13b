import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Mail, Phone, MapPin, Send, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useScrollAnimation, useStaggeredAnimation } from "@/hooks/useScrollAnimation";

const Contact = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const headerRef = useScrollAnimation();
  const formRef = useStaggeredAnimation(200);
  const contactInfoRef = useStaggeredAnimation(400);

  const contactInfo = [
    {
      icon: <Mail className="w-6 h-6" />,
      title: "Email",
      content: "<EMAIL>",
      href: "mailto:<EMAIL>",
      description: "Send me an email anytime"
    },
    {
      icon: <Phone className="w-6 h-6" />,
      title: "Phone",
      content: "+254716830746",
      href: "tel:+254716830746",
      description: "Call me for urgent matters"
    },
    {
      icon: <MapPin className="w-6 h-6" />,
      title: "Location",
      content: "Home Office, Nairobi",
      href: "#",
      description: "Available for remote meetings"
    }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Here you would typically send the form data to your Node.js/Express backend
    console.log("Form submitted:", formData);
    
    toast({
      title: "Message sent successfully!",
      description: "Thank you for your message. I'll get back to you within 24 hours.",
    });
    
    // Reset form
    setFormData({
      name: "",
      email: "",
      subject: "",
      message: ""
    });
    setIsSubmitting(false);
  };

  return (
    <section id="contact" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/20 to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-full h-px bg-gradient-to-r from-transparent via-accent/20 to-transparent"></div>
        <div className="absolute top-1/4 right-20 w-80 h-80 bg-primary/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 left-20 w-96 h-96 bg-accent/5 rounded-full blur-3xl animate-float delay-1000"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div 
          ref={headerRef}
          className="text-center space-y-6 mb-16 opacity-0 translate-y-8"
        >
          <div className="inline-block">
            <h2 className="text-4xl lg:text-5xl font-bold text-foreground relative">
              Get In Touch
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-1 gradient-primary rounded-full animate-scale-in delay-300"></div>
            </h2>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto hover:text-foreground transition-colors duration-300 cursor-default">
            Ready to transform your ideas into digital reality? Let's discuss your next project.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Contact Information */}
          <div 
            ref={contactInfoRef}
            className="space-y-8 opacity-0 translate-y-8"
          >
            <div>
              <h3 className="text-2xl font-semibold text-foreground mb-6">
                Contact Information
              </h3>
              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <a
                    key={index}
                    href={info.href}
                    className="group flex items-start space-x-4 p-4 rounded-xl hover:bg-secondary/50 transition-all duration-300 hover:scale-105 cursor-pointer"
                    style={{
                      animationDelay: `${600 + index * 200}ms`
                    }}
                  >
                    <div className="flex-shrink-0 p-3 rounded-lg gradient-primary text-primary-foreground group-hover:scale-110 group-hover:rotate-6 transition-all duration-300">
                      {info.icon}
                    </div>
                    <div className="space-y-1">
                      <h4 className="font-medium text-foreground group-hover:gradient-primary group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-accent group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300">
                        {info.title}
                      </h4>
                      <p className="text-muted-foreground group-hover:text-foreground transition-colors duration-300">{info.content}</p>
                      <p className="text-xs text-muted-foreground">{info.description}</p>
                    </div>
                  </a>
                ))}
              </div>
            </div>

            {/* Call to Action */}
            <Card className="gradient-card border-border/50 hover:border-primary/30 hover:shadow-glow transition-all duration-500 group">
              <CardHeader>
                <CardTitle className="text-foreground group-hover:gradient-primary group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-accent group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300">
                  Ready to start your project?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4 group-hover:text-foreground transition-colors duration-300">
                  Let's discuss how I can help bring your vision to life with cutting-edge technology and innovative solutions.
                </p>
                <Button 
                  className="gradient-primary text-primary-foreground border-0 hover:shadow-glow transition-smooth w-full group/btn"
                >
                  <span className="mr-2">Schedule a Call</span>
                  <Phone className="w-4 h-4 group-hover/btn:animate-bounce" />
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Contact Form */}
          <div 
            ref={formRef}
            className="lg:col-span-2 opacity-0 translate-y-8"
          >
            <Card className="gradient-card border-border/50 hover:border-primary/30 hover:shadow-glow transition-all duration-500">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground flex items-center">
                  <Send className="w-6 h-6 mr-3 text-primary" />
                  Send me a message
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label htmlFor="name" className="block text-sm font-medium text-foreground">
                        Name *
                      </label>
                      <Input
                        id="name"
                        name="name"
                        type="text"
                        required
                        value={formData.name}
                        onChange={handleInputChange}
                        className="bg-background border-border focus:border-primary hover:border-primary/50 transition-colors"
                        placeholder="Your full name"
                      />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="email" className="block text-sm font-medium text-foreground">
                        Email *
                      </label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        required
                        value={formData.email}
                        onChange={handleInputChange}
                        className="bg-background border-border focus:border-primary hover:border-primary/50 transition-colors"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <label htmlFor="subject" className="block text-sm font-medium text-foreground">
                      Subject *
                    </label>
                    <Input
                      id="subject"
                      name="subject"
                      type="text"
                      required
                      value={formData.subject}
                      onChange={handleInputChange}
                      className="bg-background border-border focus:border-primary hover:border-primary/50 transition-colors"
                      placeholder="Project inquiry, collaboration, etc."
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label htmlFor="message" className="block text-sm font-medium text-foreground">
                      Message *
                    </label>
                    <Textarea
                      id="message"
                      name="message"
                      required
                      value={formData.message}
                      onChange={handleInputChange}
                      className="bg-background border-border focus:border-primary hover:border-primary/50 transition-colors min-h-32 resize-none"
                      placeholder="Tell me about your project, goals, and how I can help..."
                    />
                  </div>
                  
                  <Button 
                    type="submit"
                    size="lg"
                    disabled={isSubmitting}
                    className="gradient-primary text-primary-foreground border-0 hover:shadow-glow transition-smooth group w-full sm:w-auto relative overflow-hidden"
                  >
                    <span className="relative z-10 flex items-center">
                      {isSubmitting ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4 mr-2 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                          Send Message
                        </>
                      )}
                    </span>
                    <div className="absolute inset-0 bg-gradient-to-r from-accent to-primary opacity-0 group-hover:opacity-20 transition-opacity"></div>
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
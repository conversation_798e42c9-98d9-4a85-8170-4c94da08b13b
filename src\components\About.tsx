import { Code, Lightbulb, Users } from "lucide-react";
import { useScrollAnimation, useStaggeredAnimation } from "@/hooks/useScrollAnimation";

const About = () => {
  const aboutRef = useScrollAnimation();
  const statsRef = useStaggeredAnimation(200);
  const servicesRef = useStaggeredAnimation(400);

  const stats = [
    { number: "120", suffix: "+", label: "Completed Projects" },
    { number: "95", suffix: "%", label: "Client Satisfaction" },
    { number: "10", suffix: "+", label: "Years of Experience" },
  ];

  const services = [
    {
      icon: <Code className="w-8 h-8" />,
      title: "Website Development",
      description: "Custom web applications and websites built with modern technologies and best practices for optimal performance."
    },
    {
      icon: <Lightbulb className="w-8 h-8" />,
      title: "Business Analysis", 
      description: "Strategic analysis and solutions to optimize your business processes and achieve measurable results."
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Digital Consulting",
      description: "Comprehensive digital transformation guidance to help businesses unlock their full potential in the digital age."
    }
  ];

  return (
    <section id="about" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/20 to-transparent"></div>
        <div className="absolute top-20 right-20 w-64 h-64 bg-accent/5 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div 
            ref={aboutRef}
            className="space-y-8 opacity-0 translate-y-8"
          >
            <div className="space-y-6">
              <div className="inline-block">
                <h2 className="text-4xl lg:text-5xl font-bold text-foreground relative">
                  About me
                  <div className="absolute -bottom-2 left-0 w-16 h-1 gradient-primary rounded-full animate-scale-in delay-300"></div>
                </h2>
              </div>
              <p className="text-lg text-muted-foreground leading-relaxed hover:text-foreground transition-colors duration-300 cursor-default">
                I started my software journey from photography. Through that, I learned to 
                love the process of creating from scratch. Since then, this has led me to 
                software development as it fulfills my love for learning and building things.
              </p>
              <p className="text-lg text-muted-foreground leading-relaxed hover:text-foreground transition-colors duration-300 cursor-default">
                With expertise in web development, business strategy, and digital transformation, 
                I deliver tailored solutions that drive growth, optimize operations, and achieve 
                measurable results. Let's turn your vision into reality with cutting-edge 
                technology and data-driven strategies.
              </p>
            </div>

            {/* Stats */}
            <div 
              ref={statsRef}
              className="grid grid-cols-3 gap-8 pt-8 opacity-0 translate-y-8"
            >
              {stats.map((stat, index) => (
                <div 
                  key={index} 
                  className="text-center group cursor-default"
                  style={{
                    animationDelay: `${600 + index * 200}ms`
                  }}
                >
                  <div className="text-3xl lg:text-4xl font-bold gradient-primary bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300 animate-fade-in-up">
                    {stat.number}{stat.suffix}
                  </div>
                  <div className="text-sm text-muted-foreground mt-2 group-hover:text-foreground transition-colors duration-300">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Services */}
          <div 
            ref={servicesRef}
            className="space-y-6 opacity-0 translate-y-8"
          >
            {services.map((service, index) => (
              <div 
                key={index}
                className="group flex items-start space-x-4 p-6 rounded-2xl gradient-card border border-border/50 hover:border-primary/30 transition-all duration-500 hover:scale-105 hover:shadow-glow cursor-pointer"
                style={{
                  animationDelay: `${800 + index * 200}ms`
                }}
              >
                <div className="flex-shrink-0 p-3 rounded-xl gradient-primary text-primary-foreground group-hover:scale-110 group-hover:rotate-3 transition-all duration-300">
                  {service.icon}
                </div>
                <div className="space-y-2">
                  <h3 className="text-xl font-semibold text-foreground group-hover:gradient-primary group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-accent group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300">
                    {service.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed group-hover:text-foreground transition-colors duration-300">
                    {service.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
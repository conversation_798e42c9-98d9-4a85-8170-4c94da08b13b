import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Mail, Phone, MapPin, Send, CheckCircle, Code, Briefcase, FileText, Users, Globe, Camera, Palette, ShoppingCart } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useScrollAnimation, useStaggeredAnimation } from "@/hooks/useScrollAnimation";

const Contact = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    phone: "",
    serviceType: "",
    projectType: "",
    budget: "",
    timeline: "",
    services: [] as string[],
    subject: "",
    message: "",
    urgency: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const headerRef = useScrollAnimation();
  const formRef = useStaggeredAnimation(200);
  const contactInfoRef = useStaggeredAnimation(400);

  const serviceOptions = [
    { id: "web-dev", label: "Web Development", icon: <Code className="w-4 h-4" /> },
    { id: "business-analysis", label: "Business Analysis", icon: <Briefcase className="w-4 h-4" /> },
    { id: "resume-writing", label: "Resume Writing", icon: <FileText className="w-4 h-4" /> },
    { id: "digital-consulting", label: "Digital Consulting", icon: <Users className="w-4 h-4" /> },
    { id: "ecommerce", label: "E-commerce Solutions", icon: <ShoppingCart className="w-4 h-4" /> },
    { id: "portfolio", label: "Portfolio Website", icon: <Globe className="w-4 h-4" /> },
    { id: "photography", label: "Photography Website", icon: <Camera className="w-4 h-4" /> },
    { id: "ui-ux", label: "UI/UX Design", icon: <Palette className="w-4 h-4" /> }
  ];

  const projectTypes = [
    "New Website",
    "Website Redesign",
    "Business Analysis",
    "Resume/CV Writing",
    "Digital Strategy",
    "Consultation",
    "Maintenance & Support",
    "Other"
  ];

  const budgetRanges = [
    "Under $1,000",
    "$1,000 - $5,000",
    "$5,000 - $10,000",
    "$10,000 - $25,000",
    "$25,000+",
    "Let's discuss"
  ];

  const timelineOptions = [
    "ASAP (Rush job)",
    "Within 1 week",
    "Within 1 month",
    "1-3 months",
    "3-6 months",
    "6+ months",
    "Flexible"
  ];

  const urgencyLevels = [
    "Low - No rush",
    "Medium - Standard timeline",
    "High - Need it soon",
    "Critical - Urgent"
  ];

  const contactInfo = [
    {
      icon: <Mail className="w-6 h-6" />,
      title: "Email",
      content: "<EMAIL>",
      href: "mailto:<EMAIL>",
      description: "Send me an email anytime"
    },
    {
      icon: <Phone className="w-6 h-6" />,
      title: "Phone",
      content: "+254716830746",
      href: "tel:+254716830746",
      description: "Call me for urgent matters"
    },
    {
      icon: <MapPin className="w-6 h-6" />,
      title: "Location",
      content: "Home Office, Nairobi",
      href: "#",
      description: "Available for remote meetings"
    }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleServiceToggle = (serviceId: string) => {
    setFormData(prev => ({
      ...prev,
      services: prev.services.includes(serviceId)
        ? prev.services.filter(id => id !== serviceId)
        : [...prev.services, serviceId]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Here you would typically send the form data to your Node.js/Express backend
    console.log("Form submitted:", formData);
    
    toast({
      title: "Message sent successfully!",
      description: "Thank you for your message. I'll get back to you within 24 hours.",
    });
    
    // Reset form
    setFormData({
      name: "",
      email: "",
      company: "",
      phone: "",
      serviceType: "",
      projectType: "",
      budget: "",
      timeline: "",
      services: [],
      subject: "",
      message: "",
      urgency: ""
    });
    setIsSubmitting(false);
  };

  return (
    <section id="contact" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/20 to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-full h-px bg-gradient-to-r from-transparent via-accent/20 to-transparent"></div>
        <div className="absolute top-1/4 right-20 w-80 h-80 bg-primary/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 left-20 w-96 h-96 bg-accent/5 rounded-full blur-3xl animate-float delay-1000"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div 
          ref={headerRef}
          className="text-center space-y-6 mb-16 opacity-0 translate-y-8"
        >
          <div className="inline-block">
            <h2 className="text-4xl lg:text-5xl font-bold text-foreground relative">
              Get In Touch
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-1 gradient-primary rounded-full animate-scale-in delay-300"></div>
            </h2>
          </div>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto hover:text-foreground transition-colors duration-300 cursor-default">
            Ready to transform your ideas into digital reality? Fill out the detailed form below to help me understand your project requirements and provide you with the best possible solution.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Contact Information */}
          <div 
            ref={contactInfoRef}
            className="space-y-8 opacity-0 translate-y-8"
          >
            <div>
              <h3 className="text-2xl font-semibold text-foreground mb-6">
                Contact Information
              </h3>
              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <a
                    key={index}
                    href={info.href}
                    className="group flex items-start space-x-4 p-4 rounded-xl hover:bg-secondary/50 transition-all duration-300 hover:scale-105 cursor-pointer"
                    style={{
                      animationDelay: `${600 + index * 200}ms`
                    }}
                  >
                    <div className="flex-shrink-0 p-3 rounded-lg gradient-primary text-primary-foreground group-hover:scale-110 group-hover:rotate-6 transition-all duration-300">
                      {info.icon}
                    </div>
                    <div className="space-y-1">
                      <h4 className="font-medium text-foreground group-hover:gradient-primary group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-accent group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300">
                        {info.title}
                      </h4>
                      <p className="text-muted-foreground group-hover:text-foreground transition-colors duration-300">{info.content}</p>
                      <p className="text-xs text-muted-foreground">{info.description}</p>
                    </div>
                  </a>
                ))}
              </div>
            </div>

            {/* Call to Action */}
            <Card className="gradient-card border-border/50 hover:border-primary/30 hover:shadow-glow transition-all duration-500 group">
              <CardHeader>
                <CardTitle className="text-foreground group-hover:gradient-primary group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-accent group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300">
                  Ready to start your project?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4 group-hover:text-foreground transition-colors duration-300">
                  Let's discuss how I can help bring your vision to life with cutting-edge technology and innovative solutions.
                </p>
                <Button 
                  className="gradient-primary text-primary-foreground border-0 hover:shadow-glow transition-smooth w-full group/btn"
                >
                  <span className="mr-2">Schedule a Call</span>
                  <Phone className="w-4 h-4 group-hover/btn:animate-bounce" />
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Contact Form */}
          <div 
            ref={formRef}
            className="lg:col-span-2 opacity-0 translate-y-8"
          >
            <Card className="gradient-card border-border/50 hover:border-primary/30 hover:shadow-glow transition-all duration-500">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground flex items-center">
                  <Send className="w-6 h-6 mr-3 text-primary" />
                  Send me a message
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-8">
                  {/* Basic Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
                      Basic Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="name">Full Name *</Label>
                        <Input
                          id="name"
                          name="name"
                          type="text"
                          required
                          value={formData.name}
                          onChange={handleInputChange}
                          placeholder="Your full name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address *</Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          required
                          value={formData.email}
                          onChange={handleInputChange}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="company">Company/Organization</Label>
                        <Input
                          id="company"
                          name="company"
                          type="text"
                          value={formData.company}
                          onChange={handleInputChange}
                          placeholder="Your company name (optional)"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">Phone Number</Label>
                        <Input
                          id="phone"
                          name="phone"
                          type="tel"
                          value={formData.phone}
                          onChange={handleInputChange}
                          placeholder="+254 XXX XXX XXX"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Services Needed */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
                      Services Needed
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {serviceOptions.map((service) => (
                        <div key={service.id} className="flex items-center space-x-3 p-3 rounded-lg border border-border hover:bg-secondary/50 transition-colors">
                          <Checkbox
                            id={service.id}
                            checked={formData.services.includes(service.id)}
                            onCheckedChange={() => handleServiceToggle(service.id)}
                          />
                          <Label htmlFor={service.id} className="flex items-center space-x-2 cursor-pointer">
                            {service.icon}
                            <span>{service.label}</span>
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Project Details */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
                      Project Details
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="projectType">Project Type</Label>
                        <Select onValueChange={(value) => handleSelectChange("projectType", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select project type" />
                          </SelectTrigger>
                          <SelectContent>
                            {projectTypes.map((type) => (
                              <SelectItem key={type} value={type}>{type}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="budget">Budget Range</Label>
                        <Select onValueChange={(value) => handleSelectChange("budget", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select budget range" />
                          </SelectTrigger>
                          <SelectContent>
                            {budgetRanges.map((range) => (
                              <SelectItem key={range} value={range}>{range}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="timeline">Timeline</Label>
                        <Select onValueChange={(value) => handleSelectChange("timeline", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select timeline" />
                          </SelectTrigger>
                          <SelectContent>
                            {timelineOptions.map((timeline) => (
                              <SelectItem key={timeline} value={timeline}>{timeline}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="urgency">Priority Level</Label>
                        <Select onValueChange={(value) => handleSelectChange("urgency", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                          <SelectContent>
                            {urgencyLevels.map((level) => (
                              <SelectItem key={level} value={level}>{level}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  {/* Message Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
                      Project Description
                    </h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="subject">Subject *</Label>
                        <Input
                          id="subject"
                          name="subject"
                          type="text"
                          required
                          value={formData.subject}
                          onChange={handleInputChange}
                          placeholder="Brief subject line for your project"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="message">Project Details *</Label>
                        <Textarea
                          id="message"
                          name="message"
                          required
                          value={formData.message}
                          onChange={handleInputChange}
                          placeholder="Please describe your project in detail. Include any specific requirements, features you need, design preferences, or any other relevant information that would help me understand your vision."
                          className="min-h-[120px]"
                        />
                      </div>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full gradient-primary text-primary-foreground hover:scale-105 transition-all duration-300 py-6 text-lg"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                        Sending Message...
                      </>
                    ) : (
                      <>
                        <Send className="w-5 h-5 mr-3" />
                        Send Project Inquiry
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
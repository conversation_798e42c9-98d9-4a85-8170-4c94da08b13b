import { ExternalLink, Github } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useScrollAnimation, useStaggeredAnimation } from "@/hooks/useScrollAnimation";

const Portfolio = () => {
  const headerRef = useScrollAnimation();
  const gridRef = useStaggeredAnimation(400);
  const buttonRef = useStaggeredAnimation(800);

  const projects = [
    {
      title: "UTU Cars",
      description: "Comprehensive automotive platform featuring car listings, dealership management, and vehicle marketplace functionality with advanced search and filtering capabilities.",
      image: "https://images.unsplash.com/photo-1549924231-f129b911e442?w=600&h=400&fit=crop&crop=center",
      technologies: ["React", "Next.js", "TypeScript", "Tailwind CSS"],
      liveUrl: "https://utucars.africa/",
      githubUrl: "#",
      category: "Featured"
    },
    {
      title: "Ray Interiors",
      description: "Elegant furniture e-commerce platform specializing in premium sofas, beds, and home furnishing solutions with modern design aesthetics.",
      image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop&crop=center",
      technologies: ["React", "Next.js", "E-commerce", "Responsive Design"],
      liveUrl: "https://rayinteriors.vercel.app/",
      githubUrl: "#",
      category: "E-commerce"
    },
    {
      title: "Kubwatano Tours",
      description: "Premium tours and lifestyle company website featuring destination packages, travel experiences, and adventure booking system.",
      image: "https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=600&h=400&fit=crop&crop=center",
      technologies: ["React", "Next.js", "Booking System", "Travel API"],
      liveUrl: "https://kubwatano-tours.vercel.app/",
      githubUrl: "#",
      category: "Travel"
    },
    {
      title: "Fine Urban Construction",
      description: "Luxury real estate platform showcasing premium properties, construction services, and property management solutions for high-end clients.",
      image: "https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=600&h=400&fit=crop&crop=center",
      technologies: ["React", "Next.js", "Real Estate", "Property Management"],
      liveUrl: "https://fineurban-con.vercel.app/",
      githubUrl: "#",
      category: "Real Estate"
    },
    {
      title: "Sweet Roll Kitchen",
      description: "Private catering company website featuring menu showcases, event planning services, and custom catering solutions for special occasions.",
      image: "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=600&h=400&fit=crop&crop=center",
      technologies: ["React", "Next.js", "Menu Management", "Event Planning"],
      liveUrl: "https://sweetrollkitchen.vercel.app/",
      githubUrl: "#",
      category: "Catering"
    },
    {
      title: "Chef Gathigia",
      description: "Professional private chef portfolio website showcasing culinary expertise, menu offerings, and personalized dining experiences.",
      image: "https://images.unsplash.com/photo-1577219491135-ce391730fb2c?w=600&h=400&fit=crop&crop=center",
      technologies: ["React", "Next.js", "Portfolio", "Culinary"],
      liveUrl: "https://chefgathigia.vercel.app/",
      githubUrl: "#",
      category: "Portfolio"
    },
    {
      title: "Kaya Red Interiors",
      description: "Professional interior design company showcasing modern design solutions, space planning services, and luxury home transformation projects.",
      image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop&crop=center",
      technologies: ["React", "Next.js", "Interior Design", "Portfolio"],
      liveUrl: "https://kaya-red.vercel.app/",
      githubUrl: "#",
      category: "Interior Design"
    },
    {
      title: "PHF Foundation",
      description: "Non-profit organization website for a foundation and charity NGO, featuring donation systems, project showcases, and community impact stories.",
      image: "https://images.unsplash.com/photo-**********-cd4628902d4a?w=600&h=400&fit=crop&crop=center",
      technologies: ["React", "Next.js", "Charity", "Donation System"],
      liveUrl: "https://phf.vercel.app/",
      githubUrl: "#",
      category: "Non-Profit"
    },
    {
      title: "MCTD Journey",
      description: "Personal health journey website documenting experiences with Mixed Connective Tissue Disease, providing resources and community support.",
      image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=600&h=400&fit=crop&crop=center",
      technologies: ["React", "Next.js", "Health", "Community"],
      liveUrl: "https://mctd.vercel.app/",
      githubUrl: "#",
      category: "Health"
    },
    {
      title: "Ichiban KE",
      description: "Professional tax compliance and financial services website offering comprehensive accounting solutions and business advisory services.",
      image: "https://images.unsplash.com/photo-**********-6726b3ff858f?w=600&h=400&fit=crop&crop=center",
      technologies: ["React", "Next.js", "Finance", "Tax Compliance"],
      liveUrl: "https://ichibanke.vercel.app/",
      githubUrl: "#",
      category: "Finance"
    },
    {
      title: "GNANNT",
      description: "Modern web application showcasing innovative design patterns and user experience solutions with cutting-edge development practices.",
      image: "https://images.unsplash.com/photo-*************-afdab827c52f?w=600&h=400&fit=crop&crop=center",
      technologies: ["React", "Next.js", "Modern Design", "UX/UI"],
      liveUrl: "https://gnannt.vercel.app/",
      githubUrl: "#",
      category: "Web App"
    }
  ];

  return (
    <section id="portfolio" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/20 to-transparent"></div>
        <div className="absolute top-1/3 left-0 w-72 h-72 bg-primary/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/3 right-0 w-80 h-80 bg-accent/5 rounded-full blur-3xl animate-float delay-1000"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div 
          ref={headerRef}
          className="text-center space-y-6 mb-16 opacity-0 translate-y-8"
        >
          <div className="inline-block">
            <h2 className="text-4xl lg:text-5xl font-bold text-foreground relative">
              Portfolio
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-1 gradient-primary rounded-full animate-scale-in delay-300"></div>
            </h2>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto hover:text-foreground transition-colors duration-300 cursor-default">
            A showcase of my recent projects and creative solutions
          </p>
        </div>

        {/* Projects Grid */}
        <div 
          ref={gridRef}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 opacity-0 translate-y-8"
        >
          {projects.map((project, index) => (
            <Card 
              key={index}
              className="group overflow-hidden gradient-card border-border/50 hover:border-primary/30 transition-all duration-500 hover:scale-105 hover:shadow-glow relative"
              style={{
                animationDelay: `${600 + index * 150}ms`
              }}
            >
              {/* Category Badge */}
              <div className="absolute top-4 left-4 z-20 px-2 py-1 text-xs font-medium bg-primary/20 text-primary-foreground rounded-full backdrop-blur-sm">
                {project.category}
              </div>

              {/* Image with overlay */}
              <div className="aspect-video overflow-hidden relative">
                <img 
                  src={project.image} 
                  alt={project.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                
                {/* Hover Actions */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 transform scale-75 group-hover:scale-100">
                  <div className="flex gap-3">
                    <Button
                      size="sm"
                      className="bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border-white/20"
                      onClick={() => window.open(project.liveUrl, '_blank')}
                    >
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      className="bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border-white/20"
                      onClick={() => window.open(project.githubUrl, '_blank')}
                    >
                      <Github className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <CardContent className="p-6 space-y-4">
                <div className="space-y-2">
                  <h3 className="text-xl font-semibold text-foreground group-hover:gradient-primary group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-accent group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300">
                    {project.title}
                  </h3>
                  <p className="text-muted-foreground text-sm leading-relaxed group-hover:text-foreground transition-colors duration-300">
                    {project.description}
                  </p>
                </div>

                {/* Technologies */}
                <div className="flex flex-wrap gap-2">
                  {project.technologies.map((tech, techIndex) => (
                    <span 
                      key={techIndex}
                      className="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded-md hover:bg-primary hover:text-primary-foreground transition-colors duration-300 cursor-default"
                      style={{
                        transitionDelay: `${techIndex * 100}ms`
                      }}
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                {/* Desktop Actions */}
                <div className="flex gap-3 pt-2 lg:opacity-0 lg:group-hover:opacity-100 lg:transition-opacity lg:duration-300">
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex-1 hover:bg-primary hover:text-primary-foreground hover:scale-105 transition-all duration-300"
                    onClick={() => window.open(project.liveUrl, '_blank')}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Live Demo
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="hover:bg-secondary hover:scale-105 transition-all duration-300"
                    onClick={() => window.open(project.githubUrl, '_blank')}
                  >
                    <Github className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View More Button */}
        <div 
          ref={buttonRef}
          className="text-center mt-12 opacity-0 translate-y-8"
        >
          <Button 
            variant="outline" 
            size="lg"
            className="border-primary/30 text-primary hover:bg-primary hover:text-primary-foreground hover:scale-105 hover:shadow-glow transition-all duration-500 group"
          >
            <span className="mr-2">View More Projects</span>
            <ExternalLink className="w-4 h-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Portfolio;